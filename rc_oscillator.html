<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RC正弦波振荡电路分析器</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/fft-js@0.0.12/lib/fft.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
            color: #f8fafc;
            overflow-x: hidden;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* 左侧导航栏 */
        .sidebar {
            width: 320px;
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(59, 130, 246, 0.2);
            box-shadow: 4px 0 40px rgba(0, 0, 0, 0.3);
            padding: 30px 20px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #06b6d4, #10b981);
            border-radius: 0 0 8px 8px;
        }

        .sidebar h2 {
            color: #f1f5f9;
            margin-bottom: 40px;
            font-size: 1.8em;
            font-weight: 700;
            text-align: center;
            position: relative;
            padding-bottom: 15px;
        }

        .sidebar h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #06b6d4);
            border-radius: 2px;
        }

        .nav-item {
            padding: 18px 24px;
            margin: 12px 0;
            background: rgba(30, 41, 59, 0.6);
            color: #cbd5e1;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(59, 130, 246, 0.1);
            position: relative;
            overflow: hidden;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .nav-item:hover::before {
            left: 100%;
        }

        .nav-item:hover {
            transform: translateX(8px);
            background: rgba(59, 130, 246, 0.15);
            border-color: rgba(59, 130, 246, 0.4);
            color: #f1f5f9;
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.2);
        }

        .nav-item.active {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(6, 182, 212, 0.2));
            border-color: rgba(59, 130, 246, 0.6);
            color: #f1f5f9;
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            padding: 40px;
            overflow-y: auto;
            background: rgba(15, 23, 42, 0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
        }

        .header h1 {
            font-size: 3.2em;
            font-weight: 800;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #f1f5f9, #3b82f6, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: fadeInDown 1s ease;
            letter-spacing: -1px;
        }

        .header p {
            font-size: 1.3em;
            color: #94a3b8;
            font-weight: 400;
            animation: fadeInUp 1s ease 0.3s both;
            letter-spacing: 0.5px;
        }

        /* 参数控制面板 */
        .control-panel {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: slideInLeft 0.8s ease;
            position: relative;
            overflow: hidden;
        }

        .control-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #3b82f6, #06b6d4, #10b981);
        }

        .control-panel h3 {
            color: #f1f5f9;
            margin-bottom: 30px;
            font-size: 1.6em;
            font-weight: 700;
            text-align: center;
            letter-spacing: 0.5px;
        }

        .parameter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin-bottom: 30px;
        }

        .parameter-group {
            background: rgba(15, 23, 42, 0.6);
            padding: 24px;
            border-radius: 16px;
            border: 1px solid rgba(59, 130, 246, 0.2);
            transition: all 0.3s ease;
            position: relative;
        }

        .parameter-group:hover {
            border-color: rgba(59, 130, 246, 0.4);
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.1);
        }

        .parameter-group label {
            display: block;
            margin-bottom: 12px;
            color: #cbd5e1;
            font-weight: 600;
            font-size: 0.95em;
            letter-spacing: 0.5px;
        }

        .parameter-group input {
            width: 100%;
            padding: 16px 20px;
            background: rgba(15, 23, 42, 0.8);
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 12px;
            font-size: 16px;
            color: #f1f5f9;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .parameter-group input:focus {
            border-color: #3b82f6;
            outline: none;
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
            background: rgba(15, 23, 42, 0.9);
        }

        .calculate-btn {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            color: white;
            border: none;
            padding: 18px 40px;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .calculate-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .calculate-btn:hover::before {
            left: 100%;
        }

        .calculate-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(59, 130, 246, 0.4);
        }

        /* 结果显示 */
        .results {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 24px;
            padding: 35px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: slideInRight 0.8s ease;
            position: relative;
        }

        .results::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #10b981, #06b6d4, #3b82f6);
        }

        .results h3 {
            color: #f1f5f9;
            margin-bottom: 25px;
            font-size: 1.6em;
            font-weight: 700;
            text-align: center;
            letter-spacing: 0.5px;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            margin: 8px 0;
            background: rgba(15, 23, 42, 0.6);
            border-radius: 12px;
            border: 1px solid rgba(59, 130, 246, 0.1);
            transition: all 0.3s ease;
        }

        .result-item:hover {
            border-color: rgba(59, 130, 246, 0.3);
            transform: translateX(4px);
        }

        .result-item span:first-child {
            color: #cbd5e1;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .result-item span:last-child {
            color: #3b82f6;
            font-weight: 700;
            font-size: 1.1em;
        }

        /* 图表容器 */
        .charts-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-top: 40px;
        }

        .chart-wrapper {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 24px;
            padding: 35px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: fadeInUp 1s ease;
            position: relative;
            overflow: hidden;
        }

        .chart-wrapper::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #3b82f6, #06b6d4);
        }

        .chart-wrapper h4 {
            color: #f1f5f9;
            margin-bottom: 25px;
            text-align: center;
            font-size: 1.4em;
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        /* 动画效果 */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* 添加粒子背景效果 */
        .main-content::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(30, 41, 59, 0.3);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #2563eb, #0891b2);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .sidebar {
                width: 280px;
            }

            .parameter-grid {
                grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                order: 2;
                padding: 20px;
            }

            .main-content {
                padding: 20px;
            }

            .header h1 {
                font-size: 2.5em;
            }

            .charts-container {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .parameter-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 2em;
            }

            .control-panel,
            .results,
            .chart-wrapper {
                padding: 20px;
            }

            .sidebar {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <h2>电路分析工具</h2>
            <div class="nav-item active" onclick="showSection('rc-oscillator')">
                RC振荡电路
            </div>
            <div class="nav-item" onclick="showSection('filter-analysis')">
                滤波器分析
            </div>
            <div class="nav-item" onclick="showSection('amplifier-design')">
                放大器设计
            </div>
            <div class="nav-item" onclick="showSection('frequency-response')">
                频率响应
            </div>
            <div class="nav-item" onclick="showSection('impedance-calc')">
                阻抗计算
            </div>
            <div class="nav-item" onclick="showSection('power-analysis')">
                功率分析
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="header">
                <h1>RC正弦波振荡电路分析器</h1>
                <p>专业的电路分析与波形显示工具</p>
            </div>

            <!-- 参数控制面板 -->
            <div class="control-panel">
                <h3>电路参数设置</h3>
                <div class="parameter-grid">
                    <div class="parameter-group">
                        <label for="resistance">电阻 R (Ω)</label>
                        <input type="number" id="resistance" value="1000" min="1" step="1">
                    </div>
                    <div class="parameter-group">
                        <label for="capacitance">电容 C (μF)</label>
                        <input type="number" id="capacitance" value="0.1" min="0.001" step="0.001">
                    </div>
                    <div class="parameter-group">
                        <label for="amplitude">振幅 (V)</label>
                        <input type="number" id="amplitude" value="5" min="0.1" step="0.1">
                    </div>
                    <div class="parameter-group">
                        <label for="phase">相位 (度)</label>
                        <input type="number" id="phase" value="0" min="-180" max="180" step="1">
                    </div>
                </div>
                <button class="calculate-btn" onclick="calculateAndPlot()">计算并绘制波形</button>
            </div>

            <!-- 计算结果显示 -->
            <div class="results">
                <h3>计算结果</h3>
                <div class="result-item">
                    <span>振荡频率:</span>
                    <span id="frequency">-- Hz</span>
                </div>
                <div class="result-item">
                    <span>角频率:</span>
                    <span id="angular-frequency">-- rad/s</span>
                </div>
                <div class="result-item">
                    <span>周期:</span>
                    <span id="period">-- s</span>
                </div>
                <div class="result-item">
                    <span>时间常数:</span>
                    <span id="time-constant">-- s</span>
                </div>
            </div>

            <!-- 图表显示区域 -->
            <div class="charts-container">
                <div class="chart-wrapper">
                    <h4>时域波形 (正弦波)</h4>
                    <canvas id="timeChart"></canvas>
                </div>
                <div class="chart-wrapper">
                    <h4>频域波形 (FFT)</h4>
                    <canvas id="fftChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <script>
        let timeChart = null;
        let fftChart = null;

        // 导航功能
        function showSection(sectionName) {
            // 移除所有活动状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 添加活动状态到当前项
            event.target.classList.add('active');

            // 这里可以添加切换不同功能模块的逻辑
            console.log('切换到:', sectionName);
        }

        // RC振荡电路计算函数
        function calculateRCParameters() {
            const R = parseFloat(document.getElementById('resistance').value);
            const C = parseFloat(document.getElementById('capacitance').value) * 1e-6; // 转换为法拉
            const amplitude = parseFloat(document.getElementById('amplitude').value);
            const phase = parseFloat(document.getElementById('phase').value);

            // 计算振荡频率 f = 1/(2πRC)
            const frequency = 1 / (2 * Math.PI * R * C);
            const angularFrequency = 2 * Math.PI * frequency;
            const period = 1 / frequency;
            const timeConstant = R * C;

            return {
                frequency,
                angularFrequency,
                period,
                timeConstant,
                amplitude,
                phase: phase * Math.PI / 180 // 转换为弧度
            };
        }

        // 更新结果显示
        function updateResults(params) {
            document.getElementById('frequency').textContent = params.frequency.toFixed(2) + ' Hz';
            document.getElementById('angular-frequency').textContent = params.angularFrequency.toFixed(2) + ' rad/s';
            document.getElementById('period').textContent = (params.period * 1000).toFixed(2) + ' ms';
            document.getElementById('time-constant').textContent = (params.timeConstant * 1000).toFixed(2) + ' ms';
        }

        // 生成时域数据
        function generateTimeData(params) {
            const sampleRate = params.frequency * 100; // 采样率
            const duration = 3 * params.period; // 显示3个周期
            const numSamples = Math.floor(sampleRate * duration);

            const timeData = [];
            const labels = [];

            for (let i = 0; i < numSamples; i++) {
                const t = i / sampleRate;
                const value = params.amplitude * Math.sin(params.angularFrequency * t + params.phase);
                timeData.push(value);
                labels.push(t * 1000); // 转换为毫秒
            }

            return { timeData, labels };
        }

        // FFT计算 (简化版本)
        function calculateFFT(timeData, sampleRate) {
            const N = timeData.length;
            const frequencies = [];
            const magnitudes = [];

            // 简化的FFT计算 - 只计算主要频率分量
            for (let k = 0; k < N/2; k++) {
                const freq = k * sampleRate / N;
                let real = 0, imag = 0;

                for (let n = 0; n < N; n++) {
                    const angle = -2 * Math.PI * k * n / N;
                    real += timeData[n] * Math.cos(angle);
                    imag += timeData[n] * Math.sin(angle);
                }

                const magnitude = Math.sqrt(real * real + imag * imag) / N;
                frequencies.push(freq);
                magnitudes.push(magnitude);
            }

            return { frequencies, magnitudes };
        }

        // 绘制时域图表
        function plotTimeChart(timeData, labels) {
            const ctx = document.getElementById('timeChart').getContext('2d');

            if (timeChart) {
                timeChart.destroy();
            }

            timeChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels.map(t => t.toFixed(2)),
                    datasets: [{
                        label: '电压 (V)',
                        data: timeData,
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                color: '#cbd5e1',
                                font: {
                                    size: 14,
                                    weight: '600'
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '时间 (ms)',
                                color: '#cbd5e1',
                                font: {
                                    size: 14,
                                    weight: '600'
                                }
                            },
                            grid: {
                                color: 'rgba(59, 130, 246, 0.1)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '电压 (V)',
                                color: '#cbd5e1',
                                font: {
                                    size: 14,
                                    weight: '600'
                                }
                            },
                            grid: {
                                color: 'rgba(59, 130, 246, 0.1)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    }
                }
            });
        }

        // 绘制FFT图表
        function plotFFTChart(frequencies, magnitudes) {
            const ctx = document.getElementById('fftChart').getContext('2d');

            if (fftChart) {
                fftChart.destroy();
            }

            // 只显示前50个频率分量以便观察
            const displayFreq = frequencies.slice(0, 50);
            const displayMag = magnitudes.slice(0, 50);

            fftChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: displayFreq.map(f => f.toFixed(1)),
                    datasets: [{
                        label: '幅度',
                        data: displayMag,
                        backgroundColor: 'rgba(59, 130, 246, 0.8)',
                        borderColor: '#3b82f6',
                        borderWidth: 2,
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                color: '#cbd5e1',
                                font: {
                                    size: 14,
                                    weight: '600'
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '频率 (Hz)',
                                color: '#cbd5e1',
                                font: {
                                    size: 14,
                                    weight: '600'
                                }
                            },
                            grid: {
                                color: 'rgba(59, 130, 246, 0.1)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '幅度',
                                color: '#cbd5e1',
                                font: {
                                    size: 14,
                                    weight: '600'
                                }
                            },
                            grid: {
                                color: 'rgba(59, 130, 246, 0.1)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    }
                }
            });
        }

        // 主计算和绘图函数
        function calculateAndPlot() {
            try {
                // 计算RC参数
                const params = calculateRCParameters();

                // 更新结果显示
                updateResults(params);

                // 生成时域数据
                const { timeData, labels } = generateTimeData(params);

                // 计算FFT
                const sampleRate = params.frequency * 100;
                const { frequencies, magnitudes } = calculateFFT(timeData, sampleRate);

                // 绘制图表
                plotTimeChart(timeData, labels);
                plotFFTChart(frequencies, magnitudes);

                // 添加成功提示动画
                const btn = document.querySelector('.calculate-btn');
                btn.style.background = 'linear-gradient(45deg, #28a745, #20c997)';
                btn.textContent = '计算完成!';

                setTimeout(() => {
                    btn.style.background = 'linear-gradient(45deg, #4a90e2, #357abd)';
                    btn.textContent = '计算并绘制波形';
                }, 2000);

            } catch (error) {
                console.error('计算错误:', error);
                alert('计算过程中出现错误，请检查输入参数！');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始计算和绘图
            calculateAndPlot();

            // 为输入框添加实时更新功能
            const inputs = document.querySelectorAll('input[type="number"]');
            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    // 延迟计算以避免频繁更新
                    clearTimeout(this.updateTimeout);
                    this.updateTimeout = setTimeout(calculateAndPlot, 500);
                });
            });
        });
    </script>
</body>
</html>
