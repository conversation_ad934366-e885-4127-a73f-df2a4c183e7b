<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RC正弦波振荡电路分析器</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/fft-js@0.0.12/lib/fft.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* 左侧导航栏 */
        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
            padding: 20px;
            transition: all 0.3s ease;
        }

        .sidebar h2 {
            color: #2c5aa0;
            margin-bottom: 30px;
            font-size: 1.5em;
            text-align: center;
            border-bottom: 2px solid #4a90e2;
            padding-bottom: 10px;
        }

        .nav-item {
            padding: 15px 20px;
            margin: 10px 0;
            background: linear-gradient(45deg, #4a90e2, #357abd);
            color: white;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
        }

        .nav-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
        }

        .nav-item.active {
            background: linear-gradient(45deg, #2c5aa0, #1e3d72);
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            animation: fadeInDown 1s ease;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
            animation: fadeInUp 1s ease 0.3s both;
        }

        /* 参数控制面板 */
        .control-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            animation: slideInLeft 0.8s ease;
        }

        .control-panel h3 {
            color: #2c5aa0;
            margin-bottom: 20px;
            font-size: 1.4em;
        }

        .parameter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .parameter-group {
            background: linear-gradient(145deg, #f8f9ff, #e8f0ff);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid #e0e8f0;
        }

        .parameter-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c5aa0;
            font-weight: 600;
        }

        .parameter-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .parameter-group input:focus {
            border-color: #4a90e2;
            outline: none;
            box-shadow: 0 0 10px rgba(74, 144, 226, 0.3);
        }

        .calculate-btn {
            background: linear-gradient(45deg, #4a90e2, #357abd);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
        }

        .calculate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
        }

        /* 结果显示 */
        .results {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            animation: slideInRight 0.8s ease;
        }

        .results h3 {
            color: #2c5aa0;
            margin-bottom: 15px;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        /* 图表容器 */
        .charts-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }

        .chart-wrapper {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            animation: fadeInUp 1s ease;
        }

        .chart-wrapper h4 {
            color: #2c5aa0;
            margin-bottom: 20px;
            text-align: center;
        }

        /* 动画效果 */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                order: 2;
            }
            
            .charts-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <h2>电路分析工具</h2>
            <div class="nav-item active" onclick="showSection('rc-oscillator')">
                RC振荡电路
            </div>
            <div class="nav-item" onclick="showSection('filter-analysis')">
                滤波器分析
            </div>
            <div class="nav-item" onclick="showSection('amplifier-design')">
                放大器设计
            </div>
            <div class="nav-item" onclick="showSection('frequency-response')">
                频率响应
            </div>
            <div class="nav-item" onclick="showSection('impedance-calc')">
                阻抗计算
            </div>
            <div class="nav-item" onclick="showSection('power-analysis')">
                功率分析
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="header">
                <h1>RC正弦波振荡电路分析器</h1>
                <p>专业的电路分析与波形显示工具</p>
            </div>

            <!-- 参数控制面板 -->
            <div class="control-panel">
                <h3>电路参数设置</h3>
                <div class="parameter-grid">
                    <div class="parameter-group">
                        <label for="resistance">电阻 R (Ω)</label>
                        <input type="number" id="resistance" value="1000" min="1" step="1">
                    </div>
                    <div class="parameter-group">
                        <label for="capacitance">电容 C (μF)</label>
                        <input type="number" id="capacitance" value="0.1" min="0.001" step="0.001">
                    </div>
                    <div class="parameter-group">
                        <label for="amplitude">振幅 (V)</label>
                        <input type="number" id="amplitude" value="5" min="0.1" step="0.1">
                    </div>
                    <div class="parameter-group">
                        <label for="phase">相位 (度)</label>
                        <input type="number" id="phase" value="0" min="-180" max="180" step="1">
                    </div>
                </div>
                <button class="calculate-btn" onclick="calculateAndPlot()">计算并绘制波形</button>
            </div>

            <!-- 计算结果显示 -->
            <div class="results">
                <h3>计算结果</h3>
                <div class="result-item">
                    <span>振荡频率:</span>
                    <span id="frequency">-- Hz</span>
                </div>
                <div class="result-item">
                    <span>角频率:</span>
                    <span id="angular-frequency">-- rad/s</span>
                </div>
                <div class="result-item">
                    <span>周期:</span>
                    <span id="period">-- s</span>
                </div>
                <div class="result-item">
                    <span>时间常数:</span>
                    <span id="time-constant">-- s</span>
                </div>
            </div>

            <!-- 图表显示区域 -->
            <div class="charts-container">
                <div class="chart-wrapper">
                    <h4>时域波形 (正弦波)</h4>
                    <canvas id="timeChart"></canvas>
                </div>
                <div class="chart-wrapper">
                    <h4>频域波形 (FFT)</h4>
                    <canvas id="fftChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <script>
        let timeChart = null;
        let fftChart = null;

        // 导航功能
        function showSection(sectionName) {
            // 移除所有活动状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 添加活动状态到当前项
            event.target.classList.add('active');

            // 这里可以添加切换不同功能模块的逻辑
            console.log('切换到:', sectionName);
        }

        // RC振荡电路计算函数
        function calculateRCParameters() {
            const R = parseFloat(document.getElementById('resistance').value);
            const C = parseFloat(document.getElementById('capacitance').value) * 1e-6; // 转换为法拉
            const amplitude = parseFloat(document.getElementById('amplitude').value);
            const phase = parseFloat(document.getElementById('phase').value);

            // 计算振荡频率 f = 1/(2πRC)
            const frequency = 1 / (2 * Math.PI * R * C);
            const angularFrequency = 2 * Math.PI * frequency;
            const period = 1 / frequency;
            const timeConstant = R * C;

            return {
                frequency,
                angularFrequency,
                period,
                timeConstant,
                amplitude,
                phase: phase * Math.PI / 180 // 转换为弧度
            };
        }

        // 更新结果显示
        function updateResults(params) {
            document.getElementById('frequency').textContent = params.frequency.toFixed(2) + ' Hz';
            document.getElementById('angular-frequency').textContent = params.angularFrequency.toFixed(2) + ' rad/s';
            document.getElementById('period').textContent = (params.period * 1000).toFixed(2) + ' ms';
            document.getElementById('time-constant').textContent = (params.timeConstant * 1000).toFixed(2) + ' ms';
        }

        // 生成时域数据
        function generateTimeData(params) {
            const sampleRate = params.frequency * 100; // 采样率
            const duration = 3 * params.period; // 显示3个周期
            const numSamples = Math.floor(sampleRate * duration);

            const timeData = [];
            const labels = [];

            for (let i = 0; i < numSamples; i++) {
                const t = i / sampleRate;
                const value = params.amplitude * Math.sin(params.angularFrequency * t + params.phase);
                timeData.push(value);
                labels.push(t * 1000); // 转换为毫秒
            }

            return { timeData, labels };
        }

        // FFT计算 (简化版本)
        function calculateFFT(timeData, sampleRate) {
            const N = timeData.length;
            const frequencies = [];
            const magnitudes = [];

            // 简化的FFT计算 - 只计算主要频率分量
            for (let k = 0; k < N/2; k++) {
                const freq = k * sampleRate / N;
                let real = 0, imag = 0;

                for (let n = 0; n < N; n++) {
                    const angle = -2 * Math.PI * k * n / N;
                    real += timeData[n] * Math.cos(angle);
                    imag += timeData[n] * Math.sin(angle);
                }

                const magnitude = Math.sqrt(real * real + imag * imag) / N;
                frequencies.push(freq);
                magnitudes.push(magnitude);
            }

            return { frequencies, magnitudes };
        }

        // 绘制时域图表
        function plotTimeChart(timeData, labels) {
            const ctx = document.getElementById('timeChart').getContext('2d');

            if (timeChart) {
                timeChart.destroy();
            }

            timeChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels.map(t => t.toFixed(2)),
                    datasets: [{
                        label: '电压 (V)',
                        data: timeData,
                        borderColor: '#4a90e2',
                        backgroundColor: 'rgba(74, 144, 226, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '时间 (ms)',
                                color: '#2c5aa0'
                            },
                            grid: {
                                color: 'rgba(44, 90, 160, 0.1)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '电压 (V)',
                                color: '#2c5aa0'
                            },
                            grid: {
                                color: 'rgba(44, 90, 160, 0.1)'
                            }
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    }
                }
            });
        }

        // 绘制FFT图表
        function plotFFTChart(frequencies, magnitudes) {
            const ctx = document.getElementById('fftChart').getContext('2d');

            if (fftChart) {
                fftChart.destroy();
            }

            // 只显示前50个频率分量以便观察
            const displayFreq = frequencies.slice(0, 50);
            const displayMag = magnitudes.slice(0, 50);

            fftChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: displayFreq.map(f => f.toFixed(1)),
                    datasets: [{
                        label: '幅度',
                        data: displayMag,
                        backgroundColor: 'rgba(74, 144, 226, 0.7)',
                        borderColor: '#4a90e2',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '频率 (Hz)',
                                color: '#2c5aa0'
                            },
                            grid: {
                                color: 'rgba(44, 90, 160, 0.1)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '幅度',
                                color: '#2c5aa0'
                            },
                            grid: {
                                color: 'rgba(44, 90, 160, 0.1)'
                            }
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    }
                }
            });
        }

        // 主计算和绘图函数
        function calculateAndPlot() {
            try {
                // 计算RC参数
                const params = calculateRCParameters();

                // 更新结果显示
                updateResults(params);

                // 生成时域数据
                const { timeData, labels } = generateTimeData(params);

                // 计算FFT
                const sampleRate = params.frequency * 100;
                const { frequencies, magnitudes } = calculateFFT(timeData, sampleRate);

                // 绘制图表
                plotTimeChart(timeData, labels);
                plotFFTChart(frequencies, magnitudes);

                // 添加成功提示动画
                const btn = document.querySelector('.calculate-btn');
                btn.style.background = 'linear-gradient(45deg, #28a745, #20c997)';
                btn.textContent = '计算完成!';

                setTimeout(() => {
                    btn.style.background = 'linear-gradient(45deg, #4a90e2, #357abd)';
                    btn.textContent = '计算并绘制波形';
                }, 2000);

            } catch (error) {
                console.error('计算错误:', error);
                alert('计算过程中出现错误，请检查输入参数！');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始计算和绘图
            calculateAndPlot();

            // 为输入框添加实时更新功能
            const inputs = document.querySelectorAll('input[type="number"]');
            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    // 延迟计算以避免频繁更新
                    clearTimeout(this.updateTimeout);
                    this.updateTimeout = setTimeout(calculateAndPlot, 500);
                });
            });
        });
    </script>
</body>
</html>
